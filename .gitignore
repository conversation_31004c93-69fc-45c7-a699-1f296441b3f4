# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
*.lcov
.nyc_output

# Production builds
dist/
build/
out/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*.swn
*.bak
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Database
*.sqlite
*.sqlite3
*.db
prisma/migrations/dev/

# Uploads (keep directory structure)
uploads/*
!uploads/.gitkeep

# Cache
.cache/
.parcel-cache/
.next/
.nuxt/
.docusaurus/
.serverless/
.fusebox/
.dynamodb/
.tern-port
.vscode-test/
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# TypeScript
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.development.local
.env.test.local
.env.production.local
.env.local

# Next.js
.next/
out/

# Gatsby files
.cache/
public

# VuePress
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test/

# Yarn
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Certificates
*.pem
*.key
*.crt

# Backup files
*.backup
*.bak
*.old

# Lock files (keep in version control)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml