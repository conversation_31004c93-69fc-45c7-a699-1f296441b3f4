# Environment Configuration
# Copy this file to .env and update with your values

# Application
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000
API_BASE_URL=http://localhost:5000

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/southsafari
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this
JWT_REFRESH_SECRET=your-refresh-token-secret-change-this
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Email Service (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=South Safari

# Payment Processing
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
PAYFAST_MERCHANT_ID=your-payfast-merchant-id
PAYFAST_MERCHANT_KEY=your-payfast-merchant-key
PAYFAST_PASSPHRASE=your-payfast-passphrase

# File Storage (AWS S3 or compatible)
S3_BUCKET_NAME=southsafari-uploads
S3_REGION=us-east-1
S3_ACCESS_KEY_ID=your-s3-access-key
S3_SECRET_ACCESS_KEY=your-s3-secret-key
S3_ENDPOINT=https://s3.amazonaws.com

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# External APIs
GITHUB_API_TOKEN=your-github-api-token
LINKEDIN_API_KEY=your-linkedin-api-key
CURRENCY_API_KEY=your-currency-api-key

# Security
SESSION_SECRET=your-session-secret-change-this
ENCRYPTION_KEY=your-32-character-encryption-key-here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Monitoring
SENTRY_DSN=your-sentry-dsn
PROMETHEUS_PORT=9090

# Feature Flags
ENABLE_OAUTH=true
ENABLE_PAYMENT=false
ENABLE_EMAIL_VERIFICATION=true
ENABLE_2FA=false

# Development
SEED_DATABASE=false
MOCK_EXTERNAL_APIS=true