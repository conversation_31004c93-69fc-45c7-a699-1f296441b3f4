{"name": "south-safari-backend", "version": "0.1.0", "description": "Backend API for South Safari Developer Partnership Platform", "main": "dist/index.js", "author": "South Safari Team", "license": "UNLICENSED", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "format": "prettier --write 'src/**/*.ts'", "type-check": "tsc --noEmit", "db:migrate": "prisma migrate dev", "db:push": "prisma db push", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "@prisma/client": "^5.7.0", "zod": "^3.22.4", "winston": "^3.11.0", "redis": "^4.6.11", "ioredis": "^5.3.2", "bull": "^4.11.5", "multer": "^1.4.5-lts.1", "sharp": "^0.33.0", "nodemailer": "^6.9.7", "@sendgrid/mail": "^8.1.0", "stripe": "^14.8.0", "axios": "^1.6.2", "date-fns": "^2.30.0", "uuid": "^9.0.1", "express-async-errors": "^3.1.1"}, "devDependencies": {"@types/node": "^20.10.4", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "prettier": "^3.1.0", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "prisma": "^5.7.0", "jest": "^29.7.0", "@types/jest": "^29.5.11", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}