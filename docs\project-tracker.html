<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>South Safari - Project Development Tracker</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background-color: #1a1a1a;
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #888;
            font-size: 1.1em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #6366f1;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 1.1em;
        }

        .section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .section-title {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #1a1a1a;
            border-bottom: 2px solid #6366f1;
            padding-bottom: 10px;
        }

        .progress-bar {
            background-color: #e0e0e0;
            height: 30px;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #6366f1, #8b5cf6);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            transition: width 0.5s ease;
        }

        .phase-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .phase-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #6366f1;
        }

        .phase-card h3 {
            margin-bottom: 15px;
            color: #1a1a1a;
        }

        .task-list {
            list-style: none;
        }

        .task-item {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .task-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid #6366f1;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .task-checkbox.completed {
            background-color: #6366f1;
            color: white;
        }

        .milestone-timeline {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            position: relative;
            padding: 20px 0;
        }

        .milestone-timeline::before {
            content: '';
            position: absolute;
            top: 40px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e0e0e0;
            z-index: 0;
        }

        .milestone {
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .milestone-dot {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: white;
            border: 3px solid #e0e0e0;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .milestone.completed .milestone-dot {
            background: #6366f1;
            border-color: #6366f1;
            color: white;
        }

        .milestone.active .milestone-dot {
            border-color: #6366f1;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
        }

        .document-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .doc-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .doc-card:hover {
            background: #6366f1;
            color: white;
            transform: translateY(-3px);
        }

        .doc-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            margin-left: 10px;
        }

        .status-completed {
            background: #22c55e;
            color: white;
        }

        .status-in-progress {
            background: #f59e0b;
            color: white;
        }

        .status-pending {
            background: #e0e0e0;
            color: #666;
        }

        .last-updated {
            text-align: center;
            color: #666;
            margin-top: 40px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>South Safari Development Tracker</h1>
            <p class="subtitle">Real-time project progress and milestone tracking</p>
        </div>
    </header>

    <div class="container">
        <!-- Statistics Overview -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">25%</div>
                <div class="stat-label">Overall Progress</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">5/8</div>
                <div class="stat-label">Documents Created</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">Phase 1</div>
                <div class="stat-label">Current Phase</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">90 Days</div>
                <div class="stat-label">Until MVP</div>
            </div>
        </div>

        <!-- Overall Progress -->
        <div class="section">
            <h2 class="section-title">Overall Project Progress</h2>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 25%">25% Complete</div>
            </div>
            <p style="margin-top: 10px; color: #666;">Documentation phase completed. Ready to begin development.</p>
        </div>

        <!-- Milestone Timeline -->
        <div class="section">
            <h2 class="section-title">Project Milestones</h2>
            <div class="milestone-timeline">
                <div class="milestone completed">
                    <div class="milestone-dot">✓</div>
                    <strong>Planning</strong>
                    <br>
                    <small>Dec 2024</small>
                </div>
                <div class="milestone active">
                    <div class="milestone-dot">→</div>
                    <strong>MVP Dev</strong>
                    <br>
                    <small>Jan-Feb 2025</small>
                </div>
                <div class="milestone">
                    <div class="milestone-dot">2</div>
                    <strong>Enhancement</strong>
                    <br>
                    <small>Mar-May 2025</small>
                </div>
                <div class="milestone">
                    <div class="milestone-dot">3</div>
                    <strong>Launch</strong>
                    <br>
                    <small>Jun 2025</small>
                </div>
                <div class="milestone">
                    <div class="milestone-dot">4</div>
                    <strong>Scale</strong>
                    <br>
                    <small>Jul-Nov 2025</small>
                </div>
            </div>
        </div>

        <!-- Current Phase Details -->
        <div class="section">
            <h2 class="section-title">Current Phase: Documentation & Planning <span class="status-badge status-completed">Completed</span></h2>
            <div class="phase-grid">
                <div class="phase-card">
                    <h3>✅ Completed Tasks</h3>
                    <ul class="task-list">
                        <li class="task-item">
                            <div class="task-checkbox completed">✓</div>
                            <span>Product Requirements Document (PRD)</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox completed">✓</div>
                            <span>Technical Specification</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox completed">✓</div>
                            <span>Development Roadmap</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox completed">✓</div>
                            <span>Change Log System</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox completed">✓</div>
                            <span>Development Guidelines</span>
                        </li>
                    </ul>
                </div>
                <div class="phase-card">
                    <h3>🔄 In Progress</h3>
                    <ul class="task-list">
                        <li class="task-item">
                            <div class="task-checkbox">○</div>
                            <span>Project Tracking Dashboard</span>
                        </li>
                    </ul>
                </div>
                <div class="phase-card">
                    <h3>⏳ Pending Tasks</h3>
                    <ul class="task-list">
                        <li class="task-item">
                            <div class="task-checkbox">○</div>
                            <span>API Documentation Structure</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox">○</div>
                            <span>Testing Strategy Document</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Documentation Status -->
        <div class="section">
            <h2 class="section-title">Project Documentation</h2>
            <div class="document-grid">
                <div class="doc-card">
                    <div class="doc-icon">📋</div>
                    <strong>PRD</strong>
                    <br>
                    <small class="status-completed">Completed</small>
                </div>
                <div class="doc-card">
                    <div class="doc-icon">🔧</div>
                    <strong>Tech Spec</strong>
                    <br>
                    <small class="status-completed">Completed</small>
                </div>
                <div class="doc-card">
                    <div class="doc-icon">🗺️</div>
                    <strong>Roadmap</strong>
                    <br>
                    <small class="status-completed">Completed</small>
                </div>
                <div class="doc-card">
                    <div class="doc-icon">📝</div>
                    <strong>Changelog</strong>
                    <br>
                    <small class="status-completed">Completed</small>
                </div>
                <div class="doc-card">
                    <div class="doc-icon">📖</div>
                    <strong>Guidelines</strong>
                    <br>
                    <small class="status-completed">Completed</small>
                </div>
                <div class="doc-card">
                    <div class="doc-icon">📊</div>
                    <strong>Tracker</strong>
                    <br>
                    <small class="status-in-progress">In Progress</small>
                </div>
                <div class="doc-card">
                    <div class="doc-icon">🔌</div>
                    <strong>API Docs</strong>
                    <br>
                    <small class="status-pending">Pending</small>
                </div>
                <div class="doc-card">
                    <div class="doc-icon">🧪</div>
                    <strong>Testing</strong>
                    <br>
                    <small class="status-pending">Pending</small>
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="section">
            <h2 class="section-title">Next Steps</h2>
            <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6;">
                <h3 style="margin-bottom: 15px; color: #1e40af;">Immediate Actions Required:</h3>
                <ol style="margin-left: 20px; line-height: 2;">
                    <li>Complete API documentation structure</li>
                    <li>Finalize testing strategy document</li>
                    <li>Set up development environment</li>
                    <li>Initialize React.js frontend project</li>
                    <li>Set up Node.js backend with Express</li>
                    <li>Configure PostgreSQL database</li>
                    <li>Begin Phase 1 development (User Management)</li>
                </ol>
            </div>
        </div>

        <p class="last-updated">Last updated: December 6, 2024 at 14:45 UTC</p>
    </div>

    <script>
        // Auto-refresh progress data (in real app, this would fetch from API)
        function updateProgress() {
            const now = new Date();
            const lastUpdated = document.querySelector('.last-updated');
            lastUpdated.textContent = `Last updated: ${now.toLocaleDateString()} at ${now.toLocaleTimeString()}`;
        }

        // Update every 5 minutes
        setInterval(updateProgress, 300000);

        // Add click handlers for document cards
        document.querySelectorAll('.doc-card').forEach(card => {
            card.addEventListener('click', () => {
                const docName = card.querySelector('strong').textContent;
                alert(`Opening ${docName} document...`);
                // In real app, this would navigate to the document
            });
        });
    </script>
</body>
</html>