# Development Guidelines & Rules
## South Safari Developer Partnership Platform

**Version:** 1.0  
**Date:** December 2024  
**Author:** Technical Lead  
**Status:** Active

---

## Table of Contents
1. [Core Development Principles](#core-development-principles)
2. [Code Standards](#code-standards)
3. [Git Workflow](#git-workflow)
4. [Security Guidelines](#security-guidelines)
5. [API Design Rules](#api-design-rules)
6. [Database Guidelines](#database-guidelines)
7. [Testing Requirements](#testing-requirements)
8. [Documentation Standards](#documentation-standards)
9. [Performance Guidelines](#performance-guidelines)
10. [Deployment Rules](#deployment-rules)

---

## Core Development Principles

### 1. Code Quality First
- **Never compromise code quality for speed**
- Write clean, readable, and maintainable code
- Follow SOLID principles
- Implement proper error handling
- Regular code reviews are mandatory

### 2. Security by Design
- Always consider security implications
- Never store sensitive data in plain text
- Validate all user inputs
- Use parameterized queries
- Implement proper authentication and authorization

### 3. User-Centric Development
- Always consider the end user experience
- Make interfaces intuitive and accessible
- Provide clear error messages
- Optimize for performance
- Test on real devices and connections

### 4. Collaborative Approach
- Communicate changes clearly
- Document your code
- Share knowledge with the team
- Ask for help when needed
- Review others' code constructively

---

## Code Standards

### TypeScript/JavaScript

```typescript
// ✅ GOOD: Clear naming, proper types, documented
/**
 * Creates a new partnership between developer and business
 * @param developerId - UUID of the developer
 * @param projectId - UUID of the project
 * @returns Partnership object or error
 */
export async function createPartnership(
  developerId: string,
  projectId: string
): Promise<Result<Partnership, PartnershipError>> {
  // Validate inputs
  if (!isValidUUID(developerId) || !isValidUUID(projectId)) {
    return Err(new PartnershipError('Invalid UUID format'));
  }

  try {
    const partnership = await db.partnership.create({
      data: {
        developerId,
        projectId,
        status: 'active',
        createdAt: new Date()
      }
    });

    return Ok(partnership);
  } catch (error) {
    logger.error('Partnership creation failed', { developerId, projectId, error });
    return Err(new PartnershipError('Failed to create partnership'));
  }
}

// ❌ BAD: Poor naming, no types, no error handling
function mkPrtnrshp(dev, prj) {
  return db.partnership.create({ data: { dev, prj } });
}
```

### React Components

```typescript
// ✅ GOOD: Typed props, clear structure, accessible
interface ProjectCardProps {
  project: Project;
  onApply: (projectId: string) => void;
  isLoading?: boolean;
}

export const ProjectCard: React.FC<ProjectCardProps> = ({ 
  project, 
  onApply, 
  isLoading = false 
}) => {
  const { t } = useTranslation();
  
  return (
    <Card className="project-card" role="article" aria-label={project.title}>
      <CardHeader>
        <h3 className="text-xl font-semibold">{project.title}</h3>
        <Badge variant={project.status}>{t(`status.${project.status}`)}</Badge>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600">{project.description}</p>
        <div className="mt-4 flex gap-2">
          {project.skills.map((skill) => (
            <Badge key={skill} variant="outline">{skill}</Badge>
          ))}
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={() => onApply(project.id)}
          disabled={isLoading}
          aria-label={t('apply.for.project', { title: project.title })}
        >
          {isLoading ? <Spinner /> : t('apply.now')}
        </Button>
      </CardFooter>
    </Card>
  );
};
```

### CSS/Styling Rules

```css
/* ✅ GOOD: Use CSS variables, mobile-first, semantic naming */
:root {
  --color-primary: #6366f1;
  --color-secondary: #8b5cf6;
  --spacing-unit: 0.25rem;
  --border-radius: 0.375rem;
}

.project-card {
  padding: calc(var(--spacing-unit) * 4);
  border-radius: var(--border-radius);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Mobile first */
.project-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .project-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* ❌ BAD: Magic numbers, desktop-first, unclear naming */
.prj-crd {
  padding: 16px;
  border-radius: 6px;
}
```

---

## Git Workflow

### Branch Naming Convention
```
feature/description-of-feature
bugfix/description-of-bug
hotfix/critical-fix-description
release/version-number
```

### Commit Message Format
```
type(scope): subject

body (optional)

footer (optional)
```

**Types:**
- feat: New feature
- fix: Bug fix
- docs: Documentation changes
- style: Code style changes (formatting, etc)
- refactor: Code refactoring
- test: Test additions or changes
- chore: Build process or auxiliary tool changes

**Examples:**
```bash
feat(auth): add OAuth2 integration for Google login

- Implemented Google OAuth2 flow
- Added refresh token handling
- Updated user model to support OAuth providers

Closes #123
```

### Pull Request Rules
1. **Always create a PR** - Never push directly to main
2. **Keep PRs small** - Easier to review, less risk
3. **Write descriptive PR descriptions** - Include what and why
4. **Require reviews** - At least 1 approval before merge
5. **Run tests** - All tests must pass
6. **Update documentation** - If behavior changes

### Code Review Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Tests added/updated
- [ ] Documentation updated
- [ ] No sensitive data exposed
- [ ] Performance impact considered
- [ ] Security implications reviewed
- [ ] Accessibility maintained

---

## Security Guidelines

### Authentication & Authorization
```typescript
// Always verify user permissions
export const updateProject = async (req: Request, res: Response) => {
  const { projectId } = req.params;
  const userId = req.user.id;

  // Verify ownership
  const project = await db.project.findUnique({
    where: { id: projectId },
    include: { business: true }
  });

  if (!project || project.business.userId !== userId) {
    return res.status(403).json({ error: 'Forbidden' });
  }

  // Proceed with update...
};
```

### Input Validation
```typescript
// Always validate and sanitize inputs
import { z } from 'zod';

const createProjectSchema = z.object({
  title: z.string().min(10).max(100),
  description: z.string().min(50).max(5000),
  budgetMin: z.number().positive(),
  budgetMax: z.number().positive(),
  skills: z.array(z.string()).min(1).max(10),
  deadline: z.date().min(new Date())
});

export const createProject = async (req: Request, res: Response) => {
  try {
    const validatedData = createProjectSchema.parse(req.body);
    // Use validatedData, not req.body
  } catch (error) {
    return res.status(400).json({ error: 'Invalid input' });
  }
};
```

### Sensitive Data Handling
```typescript
// Never log sensitive information
logger.info('User login attempt', { 
  email: user.email,
  // ❌ NEVER: password: req.body.password
  timestamp: new Date()
});

// Use environment variables
const config = {
  jwtSecret: process.env.JWT_SECRET!,
  databaseUrl: process.env.DATABASE_URL!,
  // ❌ NEVER: jwtSecret: 'hardcoded-secret'
};
```

---

## API Design Rules

### RESTful Principles
```typescript
// Resource-based URLs
GET    /api/projects          // List projects
GET    /api/projects/:id      // Get specific project
POST   /api/projects          // Create project
PUT    /api/projects/:id      // Update project
DELETE /api/projects/:id      // Delete project

// Nested resources
GET    /api/projects/:id/applications
POST   /api/projects/:id/applications
```

### Response Format
```typescript
// Success response
{
  "success": true,
  "data": {
    "id": "123",
    "title": "E-commerce Platform"
  },
  "meta": {
    "timestamp": "2024-12-06T10:00:00Z"
  }
}

// Error response
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input provided",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  }
}

// Paginated response
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "pages": 5
  }
}
```

### HTTP Status Codes
- 200: Success
- 201: Created
- 204: No Content
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 409: Conflict
- 422: Unprocessable Entity
- 500: Internal Server Error

---

## Database Guidelines

### Schema Design
```sql
-- Use UUIDs for primary keys
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    -- NOT: id SERIAL PRIMARY KEY
);

-- Always include timestamps
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

-- Use appropriate data types
email VARCHAR(255) NOT NULL,  -- Not TEXT for emails
price DECIMAL(10,2),          -- Not FLOAT for money

-- Add constraints
CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$')
```

### Query Optimization
```typescript
// ✅ GOOD: Select only needed fields
const projects = await db.project.findMany({
  select: {
    id: true,
    title: true,
    status: true
  },
  where: { status: 'active' },
  take: 20
});

// ❌ BAD: Selecting everything
const projects = await db.project.findMany();
```

### Migrations
```bash
# Always use migrations
npm run db:migrate:create add_user_verification

# Never modify existing migrations
# Create new migrations for changes
```

---

## Testing Requirements

### Test Coverage Goals
- Unit tests: 80% minimum coverage
- Integration tests: Critical paths
- E2E tests: Key user journeys

### Test Structure
```typescript
describe('PartnershipService', () => {
  describe('createPartnership', () => {
    it('should create partnership with valid data', async () => {
      // Arrange
      const developerId = 'valid-uuid';
      const projectId = 'valid-uuid';

      // Act
      const result = await createPartnership(developerId, projectId);

      // Assert
      expect(result.isOk()).toBe(true);
      expect(result.value).toMatchObject({
        developerId,
        projectId,
        status: 'active'
      });
    });

    it('should reject invalid UUID', async () => {
      // Arrange
      const invalidId = 'not-a-uuid';

      // Act
      const result = await createPartnership(invalidId, 'valid-uuid');

      // Assert
      expect(result.isErr()).toBe(true);
      expect(result.error.message).toContain('Invalid UUID');
    });
  });
});
```

---

## Documentation Standards

### Code Documentation
```typescript
/**
 * Service for managing developer partnerships
 * @module services/partnership
 */

/**
 * Creates a new partnership between a developer and a project
 * 
 * @param {string} developerId - The UUID of the developer
 * @param {string} projectId - The UUID of the project
 * @param {PartnershipOptions} [options] - Optional configuration
 * @returns {Promise<Result<Partnership, PartnershipError>>} Partnership or error
 * 
 * @example
 * const result = await createPartnership('dev-123', 'proj-456', {
 *   startDate: new Date('2024-01-01'),
 *   contractType: 'fixed'
 * });
 * 
 * if (result.isOk()) {
 *   console.log('Partnership created:', result.value);
 * }
 */
```

### API Documentation
```yaml
/api/partnerships:
  post:
    summary: Create a new partnership
    tags: [Partnerships]
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/CreatePartnership'
    responses:
      201:
        description: Partnership created successfully
      400:
        description: Invalid input
      401:
        description: Unauthorized
```

---

## Performance Guidelines

### Frontend Performance
```typescript
// Lazy load components
const ProjectDashboard = lazy(() => import('./ProjectDashboard'));

// Memoize expensive computations
const expensiveFilter = useMemo(() => {
  return projects.filter(p => p.skills.includes(selectedSkill));
}, [projects, selectedSkill]);

// Debounce user input
const debouncedSearch = useDebouncedCallback((term: string) => {
  searchProjects(term);
}, 300);
```

### Backend Performance
```typescript
// Use pagination
const projects = await db.project.findMany({
  skip: (page - 1) * limit,
  take: limit
});

// Cache frequently accessed data
const cachedProjects = await redis.get('active-projects');
if (cachedProjects) {
  return JSON.parse(cachedProjects);
}

// Use database indexes
// CREATE INDEX idx_projects_status ON projects(status);
```

---

## Deployment Rules

### Pre-deployment Checklist
- [ ] All tests passing
- [ ] Code reviewed and approved
- [ ] Documentation updated
- [ ] Environment variables configured
- [ ] Database migrations ready
- [ ] Performance tested
- [ ] Security scan completed

### Deployment Process
1. **Never deploy on Fridays**
2. **Always have a rollback plan**
3. **Deploy to staging first**
4. **Monitor for 24 hours**
5. **Document any issues**

### Post-deployment
```bash
# Monitor logs
kubectl logs -f deployment/api

# Check metrics
curl https://api.southsafari.com/health

# Verify functionality
npm run test:e2e:production
```

---

## Development Environment Setup

### Required Tools
- Node.js 20.x LTS
- PostgreSQL 15.x
- Redis 7.x
- Docker Desktop
- VS Code with extensions

### VS Code Extensions
```json
{
  "recommendations": [
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-tslint-plugin",
    "prisma.prisma",
    "bradlc.vscode-tailwindcss"
  ]
}
```

### Environment Variables
```bash
# .env.example
NODE_ENV=development
DATABASE_URL=postgresql://user:pass@localhost:5432/southsafari
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-secret-key
API_BASE_URL=http://localhost:5000
FRONTEND_URL=http://localhost:3000
```

---

## Monitoring & Logging

### Logging Standards
```typescript
// Use structured logging
logger.info('User action', {
  userId: user.id,
  action: 'project.create',
  projectId: project.id,
  timestamp: new Date().toISOString()
});

// Log levels
logger.debug('Detailed information');
logger.info('General information');
logger.warn('Warning messages');
logger.error('Error messages', { error });
```

### Metrics to Track
- API response times
- Error rates
- User actions
- Database query times
- Cache hit rates

---

**Document Control:**
- Review Cycle: Monthly
- Next Review: January 2025
- Approval Required: Technical Lead, Team Leads