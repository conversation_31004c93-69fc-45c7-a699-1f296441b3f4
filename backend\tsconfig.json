{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "sourceMap": true, "incremental": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@controllers/*": ["src/controllers/*"], "@services/*": ["src/services/*"], "@models/*": ["src/models/*"], "@middleware/*": ["src/middleware/*"], "@utils/*": ["src/utils/*"], "@config/*": ["src/config/*"], "@types/*": ["src/types/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "coverage", "jest.config.js"]}