{"name": "south-safari-frontend", "version": "0.1.0", "private": true, "description": "Frontend for South Safari Developer Partnership Platform", "author": "South Safari Team", "license": "UNLICENSED", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write 'src/**/*.{ts,tsx,css,md}'", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "@tanstack/react-query": "^5.12.0", "axios": "^1.6.2", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "zod": "^3.22.4", "@hookform/resolvers": "^3.3.2", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "lucide-react": "^0.294.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-separator": "^1.0.3", "react-hot-toast": "^2.4.1"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "prettier": "^3.1.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "vite": "^5.0.7", "vitest": "^1.0.2", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jsdom": "^23.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}